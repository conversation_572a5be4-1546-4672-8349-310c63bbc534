<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageRecordDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="pair_url" jdbcType="VARCHAR" property="pairUrl" />
    <result column="show_img_url" jdbcType="VARCHAR" property="showImgUrl" />
    <result column="image_path" jdbcType="VARCHAR" property="imagePath" />
    <result column="image_hash" jdbcType="VARCHAR" property="imageHash" />
    <result column="metadata" jdbcType="OTHER" property="metadata" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="result" property="result" jdbcType="OTHER"/>
    <result column="agg" property="agg" jdbcType="OTHER"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, type, url, show_img_url, image_path, image_hash, metadata, create_time, modify_time,
    deleted
  </sql>

  <!-- 公共的图片查询条件 -->
  <sql id="_pageable_conditions">
    <if test="query.id != null">
      AND i.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.type != null and query.type != ''">
      AND i.type = #{query.type,jdbcType=VARCHAR}
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(i.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
  </sql>

  <!-- 公共的游标分页条件 -->
  <sql id="_cursor_conditions">
    <if test="query.cursor != null and query.direction == 'next'">
      AND i.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND i.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
  </sql>



  <!-- 公共的图片组标题关联 LEFT JOIN，用于筛选 result 字段是否存在 -->
  <sql id="_image_group_caption_join">
    LEFT JOIN image_group_caption igc ON ig.id = igc.image_group_id AND igc.deleted = false
  </sql>



  <!-- 公共的聚合筛选条件 -->
  <sql id="_agg_filter">
    <if test="query.agg != null and query.agg.size() > 0">
      <foreach collection="query.agg" item="aggItem" separator=" AND ">
        <choose>
          <when test="aggItem.edited == true">
            AND EXISTS (
              SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
              WHERE (elem->>'userId')::int = #{aggItem.userId}
              AND elem->>'editTime' IS NOT NULL
            )
          </when>
          <when test="aggItem.edited == false">
            AND NOT EXISTS (
              SELECT 1 FROM jsonb_array_elements(sub.agg) AS elem
              WHERE (elem->>'userId')::int = #{aggItem.userId}
              AND elem->>'editTime' IS NOT NULL
            )
          </when>
        </choose>
      </foreach>
    </if>
  </sql>

  <!-- 直接在主查询中使用的聚合筛选条件 -->
  <sql id="_agg_filter_direct">
    <if test="query.agg != null and query.agg.size() > 0">
      <foreach collection="query.agg" item="aggItem" separator=" AND ">
        <choose>
          <when test="aggItem.edited == true">
            AND i.id IN (
              SELECT icu_agg.image_id
              FROM (
                SELECT
                  image_id,
                  jsonb_agg(
                    jsonb_build_object(
                      'userId', user_id,
                      'editTime', modify_time
                    )
                  ) as agg_data
                FROM image_caption_user
                WHERE deleted = false
                GROUP BY image_id
              ) icu_agg
              WHERE EXISTS (
                SELECT 1 FROM jsonb_array_elements(icu_agg.agg_data) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            )
          </when>
          <when test="aggItem.edited == false">
            AND i.id NOT IN (
              SELECT icu_agg.image_id
              FROM (
                SELECT
                  image_id,
                  jsonb_agg(
                    jsonb_build_object(
                      'userId', user_id,
                      'editTime', modify_time
                    )
                  ) as agg_data
                FROM image_caption_user
                WHERE deleted = false
                GROUP BY image_id
              ) icu_agg
              WHERE EXISTS (
                SELECT 1 FROM jsonb_array_elements(icu_agg.agg_data) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            )
          </when>
        </choose>
      </foreach>
    </if>
  </sql>

  <!-- 直接在图片组主查询中使用的聚合筛选条件 -->
  <sql id="_agg_filter_direct_group">
    <if test="query.agg != null and query.agg.size() > 0">
      <foreach collection="query.agg" item="aggItem" separator=" AND ">
        <choose>
          <when test="aggItem.edited == true">
            AND ig.id IN (
              SELECT igcu_agg.image_group_id
              FROM (
                SELECT
                  image_group_id,
                  jsonb_agg(
                    jsonb_build_object(
                      'userId', user_id,
                      'editTime', modify_time
                    )
                  ) as agg_data
                FROM image_group_caption_user
                WHERE deleted = false
                GROUP BY image_group_id
              ) igcu_agg
              WHERE EXISTS (
                SELECT 1 FROM jsonb_array_elements(igcu_agg.agg_data) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            )
          </when>
          <when test="aggItem.edited == false">
            AND ig.id NOT IN (
              SELECT igcu_agg.image_group_id
              FROM (
                SELECT
                  image_group_id,
                  jsonb_agg(
                    jsonb_build_object(
                      'userId', user_id,
                      'editTime', modify_time
                    )
                  ) as agg_data
                FROM image_group_caption_user
                WHERE deleted = false
                GROUP BY image_group_id
              ) igcu_agg
              WHERE EXISTS (
                SELECT 1 FROM jsonb_array_elements(igcu_agg.agg_data) AS elem
                WHERE (elem->>'userId')::int = #{aggItem.userId}
                AND elem->>'editTime' IS NOT NULL
              )
            )
          </when>
        </choose>
      </foreach>
    </if>
  </sql>

  <!-- 公共的排序条件 -->
  <sql id="_order_by_direction">
    <choose>
      <when test="query.direction == 'prev'">
          ORDER BY i.id DESC
      </when>
      <otherwise>
          ORDER BY i.id ASC
      </otherwise>
    </choose>
  </sql>

  <select id="findPageable" resultMap="BaseResultMap">
    SELECT
      i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted,
      NULL as result,
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            'userId', icu.user_id,
            'editTime', icu.modify_time
          )
        ) FILTER (WHERE icu.user_id IS NOT NULL),
        '[]'::jsonb
      ) as agg
    FROM image i
    LEFT JOIN (
      SELECT DISTINCT ON (image_id, user_id) image_id, user_id, modify_time
      FROM image_caption_user
      WHERE deleted = false
      ORDER BY image_id, user_id, modify_time DESC
    ) icu ON i.id = icu.image_id
    <where>
      i.deleted = false
      <include refid="_pageable_conditions" />
      <include refid="_cursor_conditions" />
      <include refid="_agg_filter_direct" />
    </where>
    GROUP BY i.id, i.type, i.url, i.show_img_url, i.image_path, i.image_hash, i.metadata, i.create_time, i.modify_time, i.deleted
    <include refid="_order_by_direction" />
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="countPageable" resultType="long">
    SELECT count(DISTINCT i.id)
    FROM image i
    LEFT JOIN (
      SELECT DISTINCT ON (image_id, user_id) image_id, user_id, modify_time
      FROM image_caption_user
      WHERE deleted = false
      ORDER BY image_id, user_id, modify_time DESC
    ) icu ON i.id = icu.image_id
    <where>
      i.deleted = false
      <include refid="_pageable_conditions" />
      <include refid="_agg_filter_direct" />
    </where>
  </select>

  <!-- 公共的图片组查询条件 -->
  <sql id="_pageable_conditions_group">
    <if test="query.id != null">
      AND ig.id = #{query.id,jdbcType=BIGINT}
    </if>
    <if test="query.paired != null and query.paired == true">
      AND i2.url is not null
    </if>
    <if test="query.paired != null and query.paired == false">
      AND i2.url is null
    </if>
    <if test="query.tags != null and query.tags.size() > 0">
      AND jsonb_exists_any(ig.metadata -> 'tags', string_to_array(#{query.tagsStr,jdbcType=VARCHAR}, ','))
    </if>
    <if test="query.cursor != null and query.direction == 'next'">
      AND ig.id > #{query.cursor,jdbcType=BIGINT}
    </if>
    <if test="query.cursor != null and query.direction == 'prev'">
      AND ig.id &lt; #{query.cursor,jdbcType=BIGINT}
    </if>
    <!-- 筛选 image_group_caption 表的 result 字段中是否包含指定的 result 值 -->
    <if test="query.result != null and query.result != ''">
      AND igc.result::jsonb ?? #{query.result,jdbcType=VARCHAR}
    </if>
  </sql>

  <select id="findImageGroupPageable" resultMap="BaseResultMap">
    SELECT
      ig.id,
      'style' as type,
      i1.url,
      i2.url as pair_url,
      ig.metadata,
      ig.create_time,
      ig.modify_time,
      igc.result,
      COALESCE(
        jsonb_agg(
          jsonb_build_object(
            'userId', igcu.user_id,
            'editTime', igcu.modify_time
          )
        ) FILTER (WHERE igcu.user_id IS NOT NULL),
        '[]'::jsonb
      ) as agg
    FROM image_group ig
    LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
    LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
    <include refid="_image_group_caption_join" />
    LEFT JOIN (
      SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
      FROM image_group_caption_user
      WHERE deleted = false
      ORDER BY image_group_id, user_id, modify_time DESC
    ) igcu ON ig.id = igcu.image_group_id
    <where>
      ig.deleted = false
      <include refid="_pageable_conditions_group" />
      <include refid="_agg_filter_direct_group" />
    </where>
    GROUP BY ig.id, i1.url, i2.url, ig.metadata, ig.create_time, ig.modify_time, igc.result
    <if test="query.orderBy != null and query.orderBy != ''">
      ORDER BY ${query.orderBy}
    </if>
    ORDER BY ig.id
    LIMIT #{query.limit} OFFSET #{query.offset}
  </select>

  <select id="countImageGroupPageable" resultType="long">
    SELECT count(DISTINCT ig.id)
    FROM image_group ig
    LEFT JOIN image i1 ON (ig.image_ids ->> 0)::bigint = i1.id AND i1.deleted = false
    LEFT JOIN image i2 ON (ig.image_ids ->> 1)::bigint = i2.id AND i2.deleted = false
    <include refid="_image_group_caption_join" />
    LEFT JOIN (
      SELECT DISTINCT ON (image_group_id, user_id) image_group_id, user_id, modify_time
      FROM image_group_caption_user
      WHERE deleted = false
      ORDER BY image_group_id, user_id, modify_time DESC
    ) igcu ON ig.id = igcu.image_group_id
    <where>
      ig.deleted = false
      <include refid="_pageable_conditions_group" />
      <include refid="_agg_filter_direct_group" />
    </where>
  </select>
</mapper>
